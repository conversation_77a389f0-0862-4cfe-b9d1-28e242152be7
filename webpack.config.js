const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-ts");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const Dotenv = require("dotenv-webpack");

const varAmbienteLocal = require("./src/environments/environment.local.js");
const varAmbienteDev = require("./src/environments/environment.dev.js");
const varAmbienteHm = require("./src/environments/environment.hm.js");
const varAmbienteProd = require("./src/environments/environment.prd.js");
const CopyWebpackPlugin = require("copy-webpack-plugin");

module.exports = (webpackConfigEnv, argv) => {
  const orgName = "caixa";
  const defaultConfig = singleSpaDefaults({
    orgName,
    projectName: "sipnc-host",
    webpackConfigEnv,
    argv,
    disableHtmlGeneration: true,
  });
  let varEnvPath;
  let varAmbiente;
  if (webpackConfigEnv.env === "dev") {
    varEnvPath = "src/environments/dev.env";
    varAmbiente = varAmbienteDev;
  }

  if (webpackConfigEnv.env === "hm") {
    varEnvPath = "src/environments/hm.env";
    varAmbiente = varAmbienteHm;
  }

  if (webpackConfigEnv.env === "prd") {
    varEnvPath = "src/environments/prod.env";
    varAmbiente = varAmbienteProd;
  }
  if (webpackConfigEnv.env === "local") {
    varEnvPath = "src/environments/local.env";
    varAmbiente = varAmbienteLocal;
  }

  return merge(defaultConfig, {
    plugins: [
      new Dotenv({
        path: varEnvPath,
      }),
      new HtmlWebpackPlugin({
        inject: false,
        template: "src/index.ejs",
        publicPath: "public",
        favicon: "public/favicon.ico",
        templateParameters: {
          varAmbiente: varAmbiente,
        },
      }),
      new CopyWebpackPlugin({
        patterns: [{ from: "static", to: "static" }],
      }),
    ],
  });
};
